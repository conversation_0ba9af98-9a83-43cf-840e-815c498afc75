<template>
  <div class="billing-period-editor">
    <div v-for="(period, index) in localValue" :key="index" class="period-item">
      <el-row :gutter="10" type="flex" align="middle">
        <el-col :span="10">
          <el-date-picker
            v-model="period.billingStartDate"
            type="date"
            placeholder="选择开始日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2" style="text-align: center">
          <span>至</span>
        </el-col>
        <el-col :span="10">
          <el-date-picker
            v-model="period.billingEndDate"
            type="date"
            placeholder="选择结束日期"
            value-format="yyyy-MM-dd"
            @change="handleChange"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="2">
          <el-button
            type="danger"
            icon="el-icon-delete"
            size="mini"
            circle
            @click="removePeriod(index)"
            :disabled="localValue.length <= 1"
          />
        </el-col>
      </el-row>
    </div>

    <el-button
      type="primary"
      icon="el-icon-plus"
      size="small"
      @click="addPeriod"
      style="margin-top: 10px"
    >
      添加账单周期
    </el-button>
  </div>
</template>

<script>
export default {
  name: "BillingPeriodEditor",
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      localValue: [],
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (Array.isArray(newVal) && newVal.length > 0) {
          this.localValue = newVal.map((item) => ({
            billingStartDate: item.billingStartDate || "",
            billingEndDate: item.billingEndDate || "",
          }));
        } else {
          this.localValue = [{ billingStartDate: "", billingEndDate: "" }];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    addPeriod() {
      this.localValue.push({ billingStartDate: "", billingEndDate: "" });
      this.handleChange();
    },

    removePeriod(index) {
      if (this.localValue.length > 1) {
        this.localValue.splice(index, 1);
        this.handleChange();
      }
    },

    handleChange() {
      this.$emit("input", this.localValue);
    },
  },
};
</script>

<style scoped>
.billing-period-editor {
  width: 100%;
}

.period-item {
  margin-bottom: 10px;
}

.period-item:last-child {
  margin-bottom: 0;
}
</style>
