<!-- 发票批量上传组件 -->
<template>
  <el-dialog
    title="批量导入"
    :visible.sync="visible"
    :close-on-click-modal="false"
    width="70%"
    append-to-body
    @close="handleCancel"
  >
    <div class="upload-container">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="upload"
          :action="upload.url"
          :headers="upload.headers"
          :data="{ fileList: 'fileList' }"
          :accept="acceptTypes"
          :multiple="true"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          :http-request="customUpload"
          :show-file-list="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            支持格式：png、JPG、pdf，单个文件不超过20MB
          </div>
        </el-upload>
      </div>

      <!-- 文件列表表格 -->
      <div class="file-table-section" v-if="fileList.length > 0">
        <div class="table-header">
          <span>已上传文件列表</span>
          <el-checkbox
            v-model="selectAll"
            @change="handleSelectAll"
            :indeterminate="isIndeterminate"
          >
            全选
          </el-checkbox>
        </div>

        <el-table
          :data="fileList"
          border
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="isFileSelectable"
          >
          </el-table-column>

          <el-table-column prop="name" label="文件名" min-width="200">
            <template slot-scope="scope">
              <span :title="scope.row.name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>

          <el-table-column prop="size" label="文件大小" width="120">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.size) }}
            </template>
          </el-table-column>

          <el-table-column prop="status" label="状态" width="120">
            <template slot-scope="scope">
              <el-tag :type="getStatusType(scope.row.status)" size="small">
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeFile(scope.$index)"
                :disabled="scope.row.status === 'uploading'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 弹窗底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" :loading="parseLoading">
        取消
      </el-button>
      <el-button
        type="primary"
        @click="handleStartParse"
        :loading="parseLoading"
        :disabled="selectedFiles.length === 0 || hasUploadingFiles"
      >
        开始解析
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getToken } from "@/utils/auth";
import invoiceApi from "@/api/settlement/destinationCharge/invoice/index.js";

export default {
  name: "InvoiceUpload",
  data() {
    let baseUrl = process.env.VUE_APP_BASE_API;
    return {
      visible: false,
      parseLoading: false,
      fileList: [],
      selectedFiles: [],
      selectAll: false,
      isIndeterminate: false,
      acceptTypes: ".png,.jpg,.jpeg,.pdf,.PNG,.JPG,.JPEG,.PDF",
      maxFileSize: 20 * 1024 * 1024, // 20MB
      upload: {
        url: baseUrl + "/doc/upload",
        headers: { Authorization: "Bearer " + getToken() },
      },
    };
  },
  computed: {
    // 是否有正在上传的文件
    hasUploadingFiles() {
      return this.fileList.some((file) => file.status === "uploading");
    },
  },
  methods: {
    /**
     * 打开弹窗
     */
    open() {
      this.visible = true;
      this.resetData();
    },

    /**
     * 重置数据
     */
    resetData() {
      this.fileList = [];
      this.selectedFiles = [];
      this.selectAll = false;
      this.isIndeterminate = false;
      this.parseLoading = false;
      this.$refs.upload?.clearFiles();
    },

    /**
     * 文件选择变化处理
     */
    handleFileChange(file, fileList) {
      // 只处理新增的文件
      const existingFile = this.fileList.find((f) => f.uid === file.uid);
      if (!existingFile) {
        const newFile = {
          uid: file.uid,
          name: file.name,
          size: file.size,
          status: "ready", // ready, uploading, success, error
          file: file.raw,
          fileId: null,
        };
        this.fileList.push(newFile);
        this.uploadFile(newFile);
      }
    },

    /**
     * 文件移除处理
     */
    handleFileRemove(file, fileList) {
      const index = this.fileList.findIndex((f) => f.uid === file.uid);
      if (index > -1) {
        this.fileList.splice(index, 1);
      }
    },

    /**
     * 上传前验证
     */
    beforeUpload(file) {
      // 检查文件格式
      const isValidType = this.isValidFileType(file.name);
      if (!isValidType) {
        this.$message.error(`文件格式不支持，仅支持：png、JPG、pdf`);
        return false;
      }

      // 检查文件大小
      if (file.size > this.maxFileSize) {
        this.$message.error(`文件大小不能超过20MB`);
        return false;
      }

      return true;
    },

    /**
     * 自定义上传方法
     */
    customUpload(option) {
      const fileIndex = this.fileList.findIndex(
        (f) => f.uid === option.file.uid
      );
      if (fileIndex === -1) return;

      this.fileList[fileIndex].status = "uploading";

      const formData = new FormData();
      formData.append("fileList", option.file);

      // 使用原生 XMLHttpRequest 进行上传
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener("progress", (e) => {
        if (e.lengthComputable) {
          const percentComplete = Math.round((e.loaded / e.total) * 100);
          // 可以在这里更新进度，暂时不显示进度条
        }
      });

      xhr.addEventListener("load", () => {
        if (xhr.status === 200) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.success || response.code === "10000") {
              // 更新文件状态为成功
              this.fileList[fileIndex].status = "success";

              // 存储上传接口返回的完整数据
              if (response.data && response.data.length > 0) {
                const fileData = response.data[0];
                this.fileList[fileIndex] = {
                  ...this.fileList[fileIndex],
                  ...fileData,
                  fileId: fileData.docId, // 保持兼容性
                  name: fileData.docName || this.fileList[fileIndex].name,
                  size: this.fileList[fileIndex].size, // 保留原始大小信息
                  status: "success",
                  file: this.fileList[fileIndex].file, // 保留原始文件对象
                };
              } else {
                // 如果没有返回详细数据，至少保存文件ID
                this.fileList[fileIndex].fileId =
                  response.data?.id ||
                  response.data?.fileId ||
                  response.data?.docId;
              }

              this.$message.success(`${option.file.name} 上传成功`);
            } else {
              this.fileList[fileIndex].status = "error";
              this.$message.error(
                `${option.file.name} 上传失败：${response.message ||
                  "未知错误"}`
              );
            }
          } catch (error) {
            this.fileList[fileIndex].status = "error";
            this.$message.error(`${option.file.name} 上传失败：响应解析错误`);
          }
        } else {
          this.fileList[fileIndex].status = "error";
          this.$message.error(
            `${option.file.name} 上传失败：HTTP ${xhr.status}`
          );
        }
      });

      xhr.addEventListener("error", () => {
        this.fileList[fileIndex].status = "error";
        this.$message.error(`${option.file.name} 上传失败：网络错误`);
      });

      xhr.open("POST", this.upload.url);
      xhr.setRequestHeader("Authorization", this.upload.headers.Authorization);
      xhr.send(formData);
    },

    /**
     * 上传单个文件
     */
    uploadFile(fileItem) {
      const formData = new FormData();
      formData.append("fileList", fileItem.file);

      this.customUpload({
        file: fileItem.file,
        uid: fileItem.uid,
      });
    },

    /**
     * 验证文件类型
     */
    isValidFileType(fileName) {
      const validExtensions = [".png", ".jpg", ".jpeg", ".pdf"];
      const fileExtension = fileName
        .toLowerCase()
        .substring(fileName.lastIndexOf("."));
      return validExtensions.includes(fileExtension);
    },

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    },

    /**
     * 获取状态类型
     */
    getStatusType(status) {
      const statusMap = {
        ready: "info",
        uploading: "warning",
        success: "success",
        error: "danger",
      };
      return statusMap[status] || "info";
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        ready: "待上传",
        uploading: "上传中...",
        success: "上传成功",
        error: "上传失败",
      };
      return statusMap[status] || "未知状态";
    },

    /**
     * 判断文件是否可选择
     */
    isFileSelectable(row) {
      return row.status === "success";
    },

    /**
     * 全选处理
     */
    handleSelectAll(val) {
      const selectableFiles = this.fileList.filter(
        (file) => file.status === "success"
      );
      if (val) {
        this.selectedFiles = [...selectableFiles];
      } else {
        this.selectedFiles = [];
      }
      this.updateSelectAllStatus();
    },

    /**
     * 选择变化处理
     */
    handleSelectionChange(selection) {
      this.selectedFiles = selection;
      this.updateSelectAllStatus();
    },

    /**
     * 更新全选状态
     */
    updateSelectAllStatus() {
      const selectableFiles = this.fileList.filter(
        (file) => file.status === "success"
      );
      const selectedCount = this.selectedFiles.length;
      const selectableCount = selectableFiles.length;

      this.selectAll = selectedCount === selectableCount && selectableCount > 0;
      this.isIndeterminate =
        selectedCount > 0 && selectedCount < selectableCount;
    },

    /**
     * 移除文件
     */
    removeFile(index) {
      this.fileList.splice(index, 1);
      // 更新选中状态
      this.selectedFiles = this.selectedFiles.filter((file) =>
        this.fileList.some((f) => f.uid === file.uid)
      );
      this.updateSelectAllStatus();
    },

    /**
     * 开始解析
     */
    async handleStartParse() {
      if (this.selectedFiles.length === 0) {
        this.$message.warning("请选择要解析的文件");
        return;
      }
      console.log("selectedFiles", this.selectedFiles);
      this.parseLoading = true;

      try {
        // 构建FormData，包含所有选中的文件
        const formData = new FormData();
        this.selectedFiles.forEach((file) => {
          formData.append("files", file.file);
        });

        const result = await invoiceApi.batchImport(formData);

        if (result.success || result.code === "10000") {
          this.$message.success("发票导入成功！");

          // 导入成功后关闭弹窗并刷新数据
          this.handleCancel();
          this.$emit("uploadSuccess");
        } else {
          this.$message.error(`导入失败：${result.message || "未知错误"}`);
        }
      } catch (error) {
        console.error("导入发票失败:", error);
        this.$message.error("导入失败，请稍后重试");
      } finally {
        this.parseLoading = false;
      }
    },

    /**
     * 取消操作
     */
    handleCancel() {
      this.visible = false;
      this.resetData();
    },
  },
};
</script>

<style scoped>
.upload-container {
  padding: 20px 0;
}

.upload-section {
  margin-bottom: 20px;
}

.file-table-section {
  margin-top: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-weight: bold;
}

.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}

.dialog-footer {
  text-align: right;
}

.el-table {
  margin-top: 10px;
}

/* 拖拽上传样式优化 */
.el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  box-sizing: border-box;
  text-align: center;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  background-color: #fafafa;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.el-upload-dragger:hover {
  border-color: #409eff;
}

.el-upload-dragger .el-icon-upload {
  font-size: 28px;
  color: #c0c4cc;
  margin: 20px 0 16px;
  line-height: 50px;
}
</style>
